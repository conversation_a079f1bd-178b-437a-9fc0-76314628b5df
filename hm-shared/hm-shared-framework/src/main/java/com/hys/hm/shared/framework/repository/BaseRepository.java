package com.hys.hm.shared.framework.repository;

import com.hys.hm.shared.framework.page.PageRequest;
import com.hys.hm.shared.framework.page.PageResult;
import com.hys.hm.shared.framework.query.QueryCondition;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.NoRepositoryBean;

import java.io.Serializable;
import java.util.List;
import java.util.Optional;

/**
 * 基础仓储接口
 * 提供通用的CRUD操作和动态查询功能
 * 
 * @param <T> 实体类型
 * @param <ID> 主键类型
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-29
 */
@NoRepositoryBean
public interface BaseRepository<T, ID extends Serializable> extends JpaRepository<T, ID>, JpaSpecificationExecutor<T> {

    /**
     * 根据查询条件查找实体列表
     * 
     * @param conditions 查询条件列表
     * @return 实体列表
     */
    List<T> findByConditions(List<QueryCondition> conditions);

    /**
     * 根据查询条件分页查找实体列表
     * 
     * @param conditions 查询条件列表
     * @param pageable 分页参数
     * @return 分页结果
     */
    Page<T> findByConditions(List<QueryCondition> conditions, Pageable pageable);

    /**
     * 根据分页请求查找实体列表
     * 
     * @param pageRequest 分页请求（包含查询条件和排序）
     * @return 分页结果
     */
    PageResult<T> findByPageRequest(PageRequest pageRequest);

    /**
     * 根据查询条件统计数量
     * 
     * @param conditions 查询条件列表
     * @return 记录数量
     */
    long countByConditions(List<QueryCondition> conditions);

    /**
     * 根据查询条件检查是否存在
     * 
     * @param conditions 查询条件列表
     * @return 是否存在
     */
    boolean existsByConditions(List<QueryCondition> conditions);

    /**
     * 根据查询条件查找第一个实体
     * 
     * @param conditions 查询条件列表
     * @return 实体（可能为空）
     */
    Optional<T> findFirstByConditions(List<QueryCondition> conditions);

    /**
     * 根据字段值查找实体
     * 
     * @param fieldName 字段名
     * @param value 字段值
     * @return 实体列表
     */
    List<T> findByField(String fieldName, Object value);

    /**
     * 根据字段值查找第一个实体
     * 
     * @param fieldName 字段名
     * @param value 字段值
     * @return 实体（可能为空）
     */
    Optional<T> findFirstByField(String fieldName, Object value);

    /**
     * 根据字段值模糊查找实体列表
     * 
     * @param fieldName 字段名
     * @param value 字段值
     * @return 实体列表
     */
    List<T> findByFieldLike(String fieldName, Object value);

    /**
     * 根据字段值范围查找实体列表
     * 
     * @param fieldName 字段名
     * @param startValue 起始值
     * @param endValue 结束值
     * @return 实体列表
     */
    List<T> findByFieldBetween(String fieldName, Object startValue, Object endValue);

    /**
     * 根据字段值列表查找实体列表（IN查询）
     * 
     * @param fieldName 字段名
     * @param values 字段值列表
     * @return 实体列表
     */
    List<T> findByFieldIn(String fieldName, List<Object> values);

    /**
     * 根据多个字段值查找实体列表（AND查询）
     * 
     * @param fieldValues 字段名-值映射
     * @return 实体列表
     */
    List<T> findByFields(java.util.Map<String, Object> fieldValues);

    /**
     * 软删除实体（如果实体支持软删除）
     * 
     * @param id 实体ID
     * @return 是否删除成功
     */
    boolean softDeleteById(ID id);

    /**
     * 批量软删除实体
     * 
     * @param ids 实体ID列表
     * @return 删除成功的数量
     */
    int softDeleteByIds(List<ID> ids);

    /**
     * 恢复软删除的实体
     * 
     * @param id 实体ID
     * @return 是否恢复成功
     */
    boolean restoreById(ID id);

    /**
     * 查找未删除的实体列表
     * 
     * @return 未删除的实体列表
     */
    List<T> findAllNotDeleted();

    /**
     * 分页查找未删除的实体列表
     * 
     * @param pageable 分页参数
     * @return 分页结果
     */
    Page<T> findAllNotDeleted(Pageable pageable);

    /**
     * 根据ID查找未删除的实体
     * 
     * @param id 实体ID
     * @return 实体（可能为空）
     */
    Optional<T> findByIdNotDeleted(ID id);

    /**
     * 统计未删除的实体数量
     * 
     * @return 未删除的实体数量
     */
    long countNotDeleted();

    /**
     * 批量更新实体
     * 
     * @param entities 实体列表
     * @return 更新后的实体列表
     */
    List<T> updateAll(Iterable<T> entities);

    /**
     * 根据条件更新字段值
     * 
     * @param conditions 查询条件
     * @param fieldName 要更新的字段名
     * @param newValue 新值
     * @return 更新的记录数
     */
    int updateFieldByConditions(List<QueryCondition> conditions, String fieldName, Object newValue);

    /**
     * 根据条件批量更新多个字段
     * 
     * @param conditions 查询条件
     * @param fieldValues 字段名-值映射
     * @return 更新的记录数
     */
    int updateFieldsByConditions(List<QueryCondition> conditions, java.util.Map<String, Object> fieldValues);

    /**
     * 执行原生SQL查询
     * 
     * @param sql SQL语句
     * @param parameters 参数
     * @return 查询结果
     */
    List<Object[]> executeNativeQuery(String sql, Object... parameters);

    /**
     * 执行原生SQL更新
     * 
     * @param sql SQL语句
     * @param parameters 参数
     * @return 影响的行数
     */
    int executeNativeUpdate(String sql, Object... parameters);

    /**
     * 刷新实体管理器缓存
     */
    void flush();

    /**
     * 清空实体管理器缓存
     */
    void clear();

    /**
     * 刷新并清空缓存
     */
    void flushAndClear();
}
