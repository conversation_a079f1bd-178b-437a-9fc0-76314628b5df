package com.hys.hm.application.referral.dto;

import com.hys.hm.shared.types.enums.ReferralStatus;
import com.hys.hm.shared.types.enums.UrgencyLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 转诊查询DTO
 * 用于返回转诊表单的详细信息
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-29
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReferralQueryDTO {
    
    /**
     * 转诊ID
     */
    private String id;
    
    /**
     * 基础信息ID
     */
    private String basicInfoId;
    
    /**
     * 转诊编号
     */
    private String referralNo;
    
    /**
     * 转诊日期
     */
    private LocalDateTime referralDate;
    
    // ========== 患者信息 ==========
    
    /**
     * 患者姓名
     */
    private String patientName;
    
    /**
     * 性别：1-男，2-女
     */
    private Integer gender;
    
    /**
     * 性别描述
     */
    private String genderDesc;
    
    /**
     * 年龄
     */
    private Integer age;
    
    /**
     * 身份证号（脱敏）
     */
    private String idCard;
    
    /**
     * 联系电话（脱敏）
     */
    private String phone;
    
    /**
     * 地址
     */
    private String address;
    
    /**
     * 详细地址
     */
    private String addressDetail;
    
    /**
     * 档案编号
     */
    private String fileNumber;
    
    // ========== 医疗信息 ==========
    
    /**
     * 主要症状
     */
    private String mainSymptoms;
    
    /**
     * 初步诊断
     */
    private String preliminaryDiagnosis;
    
    /**
     * 病史摘要
     */
    private String medicalHistory;
    
    /**
     * 检查结果
     */
    private String examResults;
    
    /**
     * 治疗经过
     */
    private String treatmentHistory;
    
    /**
     * 转诊原因
     */
    private String referralReason;
    
    /**
     * 转诊目的
     */
    private String referralPurpose;
    
    // ========== 转出医院信息 ==========
    
    /**
     * 转出医院ID
     */
    private String outUnitId;
    
    /**
     * 转出医院名称
     */
    private String outUnitName;
    
    /**
     * 转出科室ID
     */
    private String outDeptId;
    
    /**
     * 转出科室名称
     */
    private String outDeptName;
    
    /**
     * 转出医生ID
     */
    private String outDoctorId;
    
    /**
     * 转出医生姓名
     */
    private String outDoctorName;
    
    /**
     * 转出医生联系方式
     */
    private String outDoctorPhone;
    
    // ========== 转入医院信息 ==========
    
    /**
     * 转入医院ID
     */
    private String inUnitId;
    
    /**
     * 转入医院名称
     */
    private String inUnitName;
    
    /**
     * 转入科室ID
     */
    private String inDeptId;
    
    /**
     * 转入科室名称
     */
    private String inDeptName;
    
    /**
     * 转入医生ID
     */
    private String inDoctorId;
    
    /**
     * 转入医生姓名
     */
    private String inDoctorName;
    
    /**
     * 转入医生联系方式
     */
    private String inDoctorPhone;
    
    // ========== 状态信息 ==========
    
    /**
     * 转诊状态
     */
    private ReferralStatus status;
    
    /**
     * 状态描述
     */
    private String statusDesc;
    
    /**
     * 拒绝原因
     */
    private String rejectReason;
    
    /**
     * 确认时间
     */
    private LocalDateTime confirmTime;
    
    /**
     * 紧急程度
     */
    private UrgencyLevel urgencyLevel;
    
    /**
     * 紧急程度描述
     */
    private String urgencyDesc;
    
    /**
     * 预约时间
     */
    private LocalDateTime appointmentTime;
    
    /**
     * 附件信息
     */
    private String attachments;
    
    /**
     * 备注信息
     */
    private String notes;
    
    // ========== 审计信息 ==========
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 创建人
     */
    private String createBy;
    
    /**
     * 更新人
     */
    private String updateBy;
    
    /**
     * 版本号
     */
    private Integer version;
    
    // ========== 业务方法 ==========
    
    /**
     * 获取性别描述
     */
    public String getGenderDesc() {
        return gender != null && gender == 1 ? "男" : "女";
    }
    
    /**
     * 获取状态描述
     */
    public String getStatusDesc() {
        return status != null ? status.getDescription() : "未知";
    }
    
    /**
     * 获取紧急程度描述
     */
    public String getUrgencyDesc() {
        return urgencyLevel != null ? urgencyLevel.getDescription() : "普通";
    }
    
    /**
     * 判断是否为待处理状态
     */
    public boolean isPending() {
        return ReferralStatus.PENDING.equals(status);
    }
    
    /**
     * 判断是否为已确认状态
     */
    public boolean isConfirmed() {
        return ReferralStatus.CONFIRMED.equals(status);
    }
    
    /**
     * 判断是否为紧急转诊
     */
    public boolean isUrgent() {
        return urgencyLevel != null && urgencyLevel.isUrgent();
    }
    
    /**
     * 判断是否为急诊转诊
     */
    public boolean isEmergency() {
        return UrgencyLevel.EMERGENCY.equals(urgencyLevel);
    }
}
