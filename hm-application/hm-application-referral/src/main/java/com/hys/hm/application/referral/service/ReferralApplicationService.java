package com.hys.hm.application.referral.service;

import com.hys.hm.application.referral.converter.ReferralDTOConverter;
import com.hys.hm.application.referral.dto.ReferralCreateDTO;
import com.hys.hm.application.referral.dto.ReferralQueryDTO;
import com.hys.hm.domain.referral.model.ReferralForm;
import com.hys.hm.domain.referral.service.ReferralDomainService;
import com.hys.hm.domain.referral.service.ReferralQueryService;
import com.hys.hm.shared.framework.service.BaseServiceImpl;
import com.hys.hm.shared.types.dto.ReferralSummaryDTO;
import com.hys.hm.shared.types.enums.ReferralStatus;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 转诊应用服务
 * 继承BaseServiceImpl获得完整的CRUD功能
 * 协调领域服务完成业务用例
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-29
 */
@Service
@Slf4j
@RequiredArgsConstructor
@Transactional
public class ReferralApplicationService extends BaseServiceImpl<ReferralForm, String> {
    
    private final ReferralDomainService referralDomainService;
    private final ReferralQueryService referralQueryService;
    private final ReferralDTOConverter dtoConverter;
    
    /**
     * 创建转诊表单
     */
    public ReferralQueryDTO createReferralForm(ReferralCreateDTO createDTO, String operatorId) {
        log.info("创建转诊表单: patientName={}, operatorId={}", createDTO.getPatientName(), operatorId);
        
        // 转换DTO为领域模型
        ReferralForm referralForm = dtoConverter.toReferralForm(createDTO);
        
        // 调用领域服务创建转诊表单
        ReferralForm createdReferral = referralDomainService.createReferralForm(referralForm, operatorId);
        
        // 转换为查询DTO返回
        ReferralQueryDTO result = dtoConverter.toQueryDTO(createdReferral);
        
        log.info("转诊表单创建成功: id={}, referralNo={}", 
                result.getId(), result.getReferralNo());
        
        return result;
    }
    
    /**
     * 根据ID获取转诊表单详情
     */
    @Transactional(readOnly = true)
    public Optional<ReferralQueryDTO> getReferralDetail(String referralId) {
        log.debug("获取转诊表单详情: referralId={}", referralId);
        
        return findById(referralId)
                .map(dtoConverter::toQueryDTO);
    }
    
    /**
     * 根据转诊编号获取转诊表单详情
     */
    @Transactional(readOnly = true)
    public Optional<ReferralQueryDTO> getReferralDetailByNo(String referralNo) {
        log.debug("根据转诊编号获取转诊表单详情: referralNo={}", referralNo);
        
        return referralQueryService.getReferralSummaryByNo(referralNo)
                .flatMap(summary -> getReferralDetail(summary.getReferralId()));
    }
    
    /**
     * 确认转诊
     */
    public ReferralQueryDTO confirmReferral(String referralId, String operatorId) {
        log.info("确认转诊: referralId={}, operatorId={}", referralId, operatorId);
        
        // 调用领域服务确认转诊
        ReferralForm confirmedReferral = referralDomainService.confirmReferral(referralId, operatorId);
        
        // 转换为查询DTO返回
        ReferralQueryDTO result = dtoConverter.toQueryDTO(confirmedReferral);
        
        log.info("转诊确认成功: referralId={}, referralNo={}", 
                referralId, result.getReferralNo());
        
        return result;
    }
    
    /**
     * 拒绝转诊
     */
    public ReferralQueryDTO rejectReferral(String referralId, String reason, String operatorId) {
        log.info("拒绝转诊: referralId={}, reason={}, operatorId={}", 
                referralId, reason, operatorId);
        
        // 调用领域服务拒绝转诊
        ReferralForm rejectedReferral = referralDomainService.rejectReferral(referralId, reason, operatorId);
        
        // 转换为查询DTO返回
        ReferralQueryDTO result = dtoConverter.toQueryDTO(rejectedReferral);
        
        log.info("转诊拒绝成功: referralId={}, referralNo={}", 
                referralId, result.getReferralNo());
        
        return result;
    }
    
    /**
     * 取消转诊
     */
    public ReferralQueryDTO cancelReferral(String referralId, String operatorId) {
        log.info("取消转诊: referralId={}, operatorId={}", referralId, operatorId);
        
        // 调用领域服务取消转诊
        ReferralForm cancelledReferral = referralDomainService.cancelReferral(referralId, operatorId);
        
        // 转换为查询DTO返回
        ReferralQueryDTO result = dtoConverter.toQueryDTO(cancelledReferral);
        
        log.info("转诊取消成功: referralId={}, referralNo={}", 
                referralId, result.getReferralNo());
        
        return result;
    }
    
    /**
     * 完成转诊
     */
    public ReferralQueryDTO completeReferral(String referralId, String operatorId) {
        log.info("完成转诊: referralId={}, operatorId={}", referralId, operatorId);
        
        // 调用领域服务完成转诊
        ReferralForm completedReferral = referralDomainService.completeReferral(referralId, operatorId);
        
        // 转换为查询DTO返回
        ReferralQueryDTO result = dtoConverter.toQueryDTO(completedReferral);
        
        log.info("转诊完成成功: referralId={}, referralNo={}", 
                referralId, result.getReferralNo());
        
        return result;
    }
    
    /**
     * 批量确认转诊
     */
    public int batchConfirmReferrals(List<String> referralIds, String operatorId) {
        log.info("批量确认转诊: 数量={}, operatorId={}", referralIds.size(), operatorId);
        
        // 调用领域服务批量确认
        int successCount = referralDomainService.batchConfirmReferrals(referralIds, operatorId);
        
        log.info("批量确认转诊完成: 总数={}, 成功={}", referralIds.size(), successCount);
        
        return successCount;
    }
    
    /**
     * 生成转诊编号
     */
    @Transactional(readOnly = true)
    public String generateReferralNo() {
        log.debug("生成转诊编号");
        
        return referralDomainService.generateReferralNo();
    }
    
    /**
     * 检查转诊编号是否存在
     */
    @Transactional(readOnly = true)
    public boolean isReferralNoExists(String referralNo) {
        log.debug("检查转诊编号是否存在: referralNo={}", referralNo);
        
        return referralQueryService.existsReferralNo(referralNo);
    }
    
    // ========== 查询方法（委托给查询服务） ==========
    
    /**
     * 获取患者的转诊记录摘要
     */
    @Transactional(readOnly = true)
    public List<ReferralSummaryDTO> getPatientReferralSummary(String patientId) {
        log.debug("获取患者转诊记录摘要: patientId={}", patientId);
        
        return referralQueryService.getPatientReferralSummary(patientId);
    }
    
    /**
     * 获取待处理的转诊摘要列表
     */
    @Transactional(readOnly = true)
    public List<ReferralSummaryDTO> getPendingReferrals() {
        log.debug("获取待处理的转诊摘要列表");
        
        return referralQueryService.getPendingReferrals();
    }
    
    /**
     * 获取紧急转诊摘要列表
     */
    @Transactional(readOnly = true)
    public List<ReferralSummaryDTO> getUrgentReferrals() {
        log.debug("获取紧急转诊摘要列表");
        
        return referralQueryService.getUrgentReferrals();
    }
    
    /**
     * 获取今日转诊摘要列表
     */
    @Transactional(readOnly = true)
    public List<ReferralSummaryDTO> getTodayReferrals() {
        log.debug("获取今日转诊摘要列表");
        
        return referralQueryService.getTodayReferrals();
    }
    
    /**
     * 根据状态获取转诊摘要列表
     */
    @Transactional(readOnly = true)
    public List<ReferralSummaryDTO> getReferralsByStatus(ReferralStatus status) {
        log.debug("根据状态获取转诊摘要列表: status={}", status);
        
        return referralQueryService.getReferralsByStatus(status);
    }
    
    /**
     * 获取转诊统计信息
     */
    @Transactional(readOnly = true)
    public Map<String, Object> getReferralStatistics(String unitId, LocalDateTime startDate, LocalDateTime endDate) {
        log.debug("获取转诊统计信息: unitId={}, startDate={}, endDate={}", unitId, startDate, endDate);
        
        return referralQueryService.getReferralStatistics(unitId, startDate, endDate);
    }
    
    // ========== BaseService钩子方法重写 ==========
    
    @Override
    public void beforeSave(ReferralForm entity) {
        log.debug("转诊表单保存前处理: id={}", entity.getId());
        // 可以在这里添加保存前的业务逻辑
    }
    
    @Override
    protected void afterSave(ReferralForm entity) {
        log.debug("转诊表单保存后处理: id={}", entity.getId());
        // 可以在这里添加保存后的业务逻辑
    }
    
    @Override
    protected void beforeUpdate(ReferralForm entity) {
        log.debug("转诊表单更新前处理: id={}", entity.getId());
        // 可以在这里添加更新前的业务逻辑
    }
    
    @Override
    protected void afterUpdate(ReferralForm entity) {
        log.debug("转诊表单更新后处理: id={}", entity.getId());
        // 可以在这里添加更新后的业务逻辑
    }
}
