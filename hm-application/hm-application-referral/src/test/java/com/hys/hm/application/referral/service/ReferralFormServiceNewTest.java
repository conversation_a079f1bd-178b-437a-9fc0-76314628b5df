package com.hys.hm.application.referral.service;

import com.hys.hm.application.referral.service.impl.ReferralFormServiceImplNew;
import com.hys.hm.infrastructure.persistence.referral.entity.ReferralFormEntityNew;
import com.hys.hm.infrastructure.persistence.referral.repository.ReferralFormRepositoryNew;
import com.hys.hm.shared.framework.page.PageRequest;
import com.hys.hm.shared.framework.page.PageResult;
import com.hys.hm.shared.framework.query.QueryCondition;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 转诊表单服务测试类（新框架）
 * 演示如何测试使用新框架的服务类
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-29
 */
@ExtendWith(MockitoExtension.class)
class ReferralFormServiceNewTest {

    @Mock
    private ReferralFormRepositoryNew referralFormRepository;

    @InjectMocks
    private ReferralFormServiceImplNew referralFormService;

    private ReferralFormEntityNew testReferral;

    @BeforeEach
    void setUp() {
        testReferral = createTestReferral();
    }

    @Test
    void testSave() {
        // Given
        when(referralFormRepository.save(any(ReferralFormEntityNew.class))).thenReturn(testReferral);

        // When
        ReferralFormEntityNew savedReferral = referralFormService.save(testReferral);

        // Then
        assertNotNull(savedReferral);
        assertEquals(testReferral.getId(), savedReferral.getId());
        assertEquals(testReferral.getPatientName(), savedReferral.getPatientName());
        verify(referralFormRepository).save(testReferral);
    }

    @Test
    void testFindById() {
        // Given
        String id = "test-id";
        when(referralFormRepository.findById(id)).thenReturn(Optional.of(testReferral));

        // When
        Optional<ReferralFormEntityNew> result = referralFormService.findById(id);

        // Then
        assertTrue(result.isPresent());
        assertEquals(testReferral.getId(), result.get().getId());
        verify(referralFormRepository).findById(id);
    }

    @Test
    void testFindByReferralNo() {
        // Given
        String referralNo = "ZZ202507290001";
        when(referralFormRepository.findByReferralNo(referralNo)).thenReturn(Optional.of(testReferral));

        // When
        Optional<ReferralFormEntityNew> result = referralFormService.findByReferralNo(referralNo);

        // Then
        assertTrue(result.isPresent());
        assertEquals(testReferral.getReferralNo(), result.get().getReferralNo());
        verify(referralFormRepository).findByReferralNo(referralNo);
    }

    @Test
    void testFindByStatus() {
        // Given
        Integer status = 1;
        List<ReferralFormEntityNew> referrals = Arrays.asList(testReferral);
        when(referralFormRepository.findByStatus(status)).thenReturn(referrals);

        // When
        List<ReferralFormEntityNew> result = referralFormService.findByStatus(status);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(testReferral.getId(), result.get(0).getId());
        verify(referralFormRepository).findByStatus(status);
    }

    @Test
    void testConfirmReferral() {
        // Given
        String id = "test-id";
        String operatorId = "operator-123";
        when(referralFormRepository.findById(id)).thenReturn(Optional.of(testReferral));
        when(referralFormRepository.save(any(ReferralFormEntityNew.class))).thenReturn(testReferral);

        // When
        boolean result = referralFormService.confirmReferral(id, operatorId);

        // Then
        assertTrue(result);
        assertEquals(2, testReferral.getStatus()); // 已确认状态
        assertNotNull(testReferral.getConfirmTime());
        verify(referralFormRepository).findById(id);
        verify(referralFormRepository).save(testReferral);
    }

    @Test
    void testConfirmReferralNotFound() {
        // Given
        String id = "non-existent-id";
        String operatorId = "operator-123";
        when(referralFormRepository.findById(id)).thenReturn(Optional.empty());

        // When
        boolean result = referralFormService.confirmReferral(id, operatorId);

        // Then
        assertFalse(result);
        verify(referralFormRepository).findById(id);
        verify(referralFormRepository, never()).save(any());
    }

    @Test
    void testRejectReferral() {
        // Given
        String id = "test-id";
        String rejectReason = "医疗资源不足";
        String operatorId = "operator-123";
        when(referralFormRepository.findById(id)).thenReturn(Optional.of(testReferral));
        when(referralFormRepository.save(any(ReferralFormEntityNew.class))).thenReturn(testReferral);

        // When
        boolean result = referralFormService.rejectReferral(id, rejectReason, operatorId);

        // Then
        assertTrue(result);
        assertEquals(3, testReferral.getStatus()); // 已拒绝状态
        assertEquals(rejectReason, testReferral.getRejectReason());
        assertNotNull(testReferral.getConfirmTime());
        verify(referralFormRepository).findById(id);
        verify(referralFormRepository).save(testReferral);
    }

    @Test
    void testGenerateReferralNo() {
        // Given
        when(referralFormRepository.findMaxReferralNoByPrefix(anyString())).thenReturn(null);

        // When
        String referralNo = referralFormService.generateReferralNo();

        // Then
        assertNotNull(referralNo);
        assertTrue(referralNo.startsWith("ZZ"));
        assertEquals(14, referralNo.length()); // ZZ + 8位日期 + 4位序号
    }

    @Test
    void testIsReferralNoExists() {
        // Given
        String referralNo = "ZZ202507290001";
        when(referralFormRepository.existsByReferralNo(referralNo)).thenReturn(true);

        // When
        boolean exists = referralFormService.isReferralNoExists(referralNo);

        // Then
        assertTrue(exists);
        verify(referralFormRepository).existsByReferralNo(referralNo);
    }

    @Test
    void testCountByStatus() {
        // Given
        Integer status = 1;
        long expectedCount = 5L;
        when(referralFormRepository.countByStatus(status)).thenReturn(expectedCount);

        // When
        long count = referralFormService.countByStatus(status);

        // Then
        assertEquals(expectedCount, count);
        verify(referralFormRepository).countByStatus(status);
    }

    @Test
    void testBatchConfirmReferrals() {
        // Given
        List<String> ids = Arrays.asList("id1", "id2", "id3");
        String operatorId = "operator-123";
        
        // Mock每个ID的查找和保存
        for (String id : ids) {
            ReferralFormEntityNew referral = createTestReferral();
            referral.setId(id);
            when(referralFormRepository.findById(id)).thenReturn(Optional.of(referral));
            when(referralFormRepository.save(any(ReferralFormEntityNew.class))).thenReturn(referral);
        }

        // When
        int successCount = referralFormService.batchConfirmReferrals(ids, operatorId);

        // Then
        assertEquals(3, successCount);
        verify(referralFormRepository, times(3)).findById(anyString());
        verify(referralFormRepository, times(3)).save(any(ReferralFormEntityNew.class));
    }

    @Test
    void testValidate() {
        // Given
        ReferralFormEntityNew invalidReferral = new ReferralFormEntityNew();
        // 缺少必要字段

        // When & Then
        assertThrows(IllegalArgumentException.class, () -> {
            referralFormService.validate(invalidReferral);
        });
    }

    @Test
    void testValidateValidReferral() {
        // Given
        ReferralFormEntityNew validReferral = createTestReferral();

        // When & Then
        assertDoesNotThrow(() -> {
            referralFormService.validate(validReferral);
        });
    }

    @Test
    void testBeforeSave() {
        // Given
        ReferralFormEntityNew referral = new ReferralFormEntityNew();
        referral.setBasicInfoId("basic-123");
        referral.setPatientName("测试患者");
        referral.setPhone("13800138000");
        referral.setGender(1);
        referral.setAge(30);
        referral.setReferralReason("测试转诊");
        referral.setOutUnitId("out-unit-123");
        referral.setOutUnitName("转出医院");
        referral.setInUnitId("in-unit-123");
        referral.setInUnitName("转入医院");

        when(referralFormRepository.findMaxReferralNoByPrefix(anyString())).thenReturn(null);
        when(referralFormRepository.existsByReferralNo(anyString())).thenReturn(false);

        // When
        referralFormService.beforeSave(referral);

        // Then
        assertNotNull(referral.getId());
        assertNotNull(referral.getReferralNo());
        assertNotNull(referral.getReferralDate());
        assertEquals(1, referral.getStatus());
        assertEquals(1, referral.getUrgencyLevel());
    }

    /**
     * 创建测试用的转诊表单实体
     */
    private ReferralFormEntityNew createTestReferral() {
        ReferralFormEntityNew referral = new ReferralFormEntityNew();
        referral.setId("test-referral-id");
        referral.setBasicInfoId("basic-info-123");
        referral.setReferralNo("ZZ202507290001");
        referral.setReferralDate(LocalDateTime.now());
        referral.setPatientName("张三");
        referral.setGender(1);
        referral.setAge(45);
        referral.setIdCard("110101198001011234");
        referral.setPhone("13800138000");
        referral.setAddress("北京市朝阳区");
        referral.setReferralReason("心脏病需要专科治疗");
        referral.setOutUnitId("out-hospital-123");
        referral.setOutUnitName("北京市第一医院");
        referral.setInUnitId("in-hospital-456");
        referral.setInUnitName("北京协和医院");
        referral.setStatus(1); // 待处理
        referral.setUrgencyLevel(1); // 普通
        return referral;
    }
}
